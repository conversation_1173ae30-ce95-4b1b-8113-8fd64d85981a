<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>姿态检测调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            position: relative;
            margin: 20px 0;
        }
        #videoCanvas {
            border: 2px solid #333;
            border-radius: 8px;
            max-width: 100%;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug-info {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 姿态检测调试页面</h1>
        
        <div class="status info" id="status">
            准备就绪 - 点击启用摄像头开始测试
        </div>

        <div class="controls">
            <button class="btn-primary" id="enableCameraBtn">启用摄像头</button>
            <button class="btn-success" id="startDetectionBtn" disabled>开始检测</button>
            <button class="btn-danger" id="stopDetectionBtn" disabled>停止检测</button>
            <button class="btn-secondary" id="toggleSkeletonBtn">切换骨骼显示</button>
            <button class="btn-warning" id="clearConsoleBtn">清空控制台</button>
            <button class="btn-warning" id="tryTensorFlowBtn" disabled>尝试TensorFlow.js</button>
            <button class="btn-secondary" id="toggleConsoleOutputBtn">切换控制台输出</button>
            <button class="btn-secondary" id="toggleLabelsBtn">切换关键点标签</button>
        </div>

        <div class="video-container">
            <canvas id="videoCanvas" width="640" height="480"></canvas>
        </div>

        <div class="debug-info" id="debugInfo">等待调试信息...</div>
    </div>

    <!-- 引入必要的库 -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.21.0/dist/tf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/pose-detection@2.0.0/dist/pose-detection.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <script src="https://unpkg.com/ml5@0.12.2/dist/ml5.min.js"></script>

    <script>
        class PoseDebugger {
            constructor() {
                this.video = null;
                this.canvas = null;
                this.ctx = null;
                this.poseNet = null;
                this.poses = [];
                this.isDetecting = false;
                this.showSkeleton = true;

                // 性能控制
                this.showConsoleOutput = false; // 默认关闭控制台输出
                this.showLabels = false; // 默认关闭标签
                this.frameSkip = 0; // 跳帧计数器
                this.lastFrameTime = 0;
                this.targetFPS = 15; // 限制最大FPS

                this.init();
            }

            init() {
                this.canvas = document.getElementById('videoCanvas');
                this.ctx = this.canvas.getContext('2d');
                
                this.setupEventListeners();
                this.log('调试器初始化完成');
            }

            setupEventListeners() {
                document.getElementById('enableCameraBtn').addEventListener('click', () => this.enableCamera());
                document.getElementById('startDetectionBtn').addEventListener('click', () => this.startDetection());
                document.getElementById('stopDetectionBtn').addEventListener('click', () => this.stopDetection());
                document.getElementById('toggleSkeletonBtn').addEventListener('click', () => this.toggleSkeleton());
                document.getElementById('clearConsoleBtn').addEventListener('click', () => this.clearConsole());
                document.getElementById('tryTensorFlowBtn').addEventListener('click', () => this.tryTensorFlowPose());
                document.getElementById('toggleConsoleOutputBtn').addEventListener('click', () => this.toggleConsoleOutput());
                document.getElementById('toggleLabelsBtn').addEventListener('click', () => this.toggleLabels());
            }

            async enableCamera() {
                try {
                    this.updateStatus('正在启用摄像头...', 'info');
                    
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: { width: 640, height: 480 }
                    });

                    this.video = document.createElement('video');
                    this.video.srcObject = stream;
                    this.video.width = 640;
                    this.video.height = 480;
                    this.video.autoplay = true;
                    this.video.muted = true;

                    this.video.addEventListener('loadeddata', () => {
                        this.log('视频数据加载完成');
                        this.log(`视频尺寸: ${this.video.videoWidth}x${this.video.videoHeight}`);
                        this.log(`视频就绪状态: ${this.video.readyState}`);
                        this.updateStatus('摄像头已启用 - 可以开始检测', 'success');
                        document.getElementById('enableCameraBtn').disabled = true;
                        document.getElementById('startDetectionBtn').disabled = false;
                        document.getElementById('tryTensorFlowBtn').disabled = false;
                    });

                    this.video.addEventListener('playing', () => {
                        this.log('视频开始播放');
                        // 在视频真正开始播放时启动预览
                        setTimeout(() => {
                            this.startPreview();
                        }, 200);
                    });

                    // 强制播放视频
                    setTimeout(() => {
                        this.video.play().then(() => {
                            this.log('✅ 视频播放命令执行成功');
                        }).catch(err => {
                            this.log('❌ 视频播放失败: ' + err.message);
                        });
                    }, 100);

                    this.video.addEventListener('loadedmetadata', () => {
                        this.log('视频元数据加载完成');
                    });

                    this.video.addEventListener('canplay', () => {
                        this.log('视频可以播放');
                    });

                    this.video.addEventListener('playing', () => {
                        this.log('视频开始播放');
                    });

                } catch (error) {
                    this.log('摄像头启用失败: ' + error.message);
                    this.updateStatus('摄像头启用失败: ' + error.message, 'error');
                }
            }

            startPreview() {
                this.log('开始预览模式');
                this.log(`Canvas尺寸: ${this.canvas.width}x${this.canvas.height}`);
                this.log(`视频尺寸: ${this.video.videoWidth}x${this.video.videoHeight}`);
                this.log(`视频就绪状态: ${this.video.readyState}`);

                let frameCount = 0;

                const drawPreview = () => {
                    if (this.video && this.video.readyState >= 2 && !this.isDetecting) {
                        try {
                            // 清除画布
                            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                            // 直接绘制视频帧（不镜像）
                            this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

                            // 绘制测试标记确认绘制循环工作
                            this.ctx.fillStyle = '#ff0000';
                            this.ctx.beginPath();
                            this.ctx.arc(50, 50, 10, 0, 2 * Math.PI);
                            this.ctx.fill();

                            frameCount++;
                            if (frameCount === 1) {
                                this.log('✅ 首帧预览绘制成功');
                            } else if (frameCount === 60) {
                                this.log(`✅ 预览正常运行，已绘制${frameCount}帧`);
                            }
                        } catch (error) {
                            this.log('❌ 预览绘制错误: ' + error.message);
                        }
                    } else if (frameCount < 5) {
                        this.log(`⚠️ 预览条件检查: video=${!!this.video}, readyState=${this.video?.readyState}, detecting=${this.isDetecting}`);
                    }

                    if (!this.isDetecting) {
                        requestAnimationFrame(drawPreview);
                    }
                };
                drawPreview();
            }

            async startDetection() {
                try {
                    this.updateStatus('正在加载PoseNet模型...', 'info');
                    this.log('开始初始化PoseNet...');

                    // 检查ml5版本和可用方法
                    this.log('ml5版本: ' + (ml5.version || 'unknown'));
                    this.log('ml5.poseNet可用: ' + (typeof ml5.poseNet !== 'undefined'));

                    // 检查视频状态
                    this.log(`视频状态检查: readyState=${this.video.readyState}, videoWidth=${this.video.videoWidth}, videoHeight=${this.video.videoHeight}`);

                    if (this.video.readyState < 2) {
                        throw new Error('视频还未准备好，无法初始化PoseNet');
                    }

                    // 如果ml5.poseNet不可用，直接使用TensorFlow.js
                    if (typeof ml5.poseNet === 'undefined') {
                        this.log('⚠️ ml5.poseNet不可用，切换到TensorFlow.js方法');
                        await this.tryTensorFlowPose();
                        return;
                    }

                    // 强制使用CPU后端
                    await tf.setBackend('cpu');
                    this.log('TensorFlow后端: ' + tf.getBackend());

                    // 尝试不同的PoseNet初始化方法
                    this.log('尝试初始化PoseNet...');

                    // 方法1: 简化配置
                    this.poseNet = await ml5.poseNet(this.video, {
                        architecture: 'MobileNetV1',
                        imageScaleFactor: 0.3,
                        outputStride: 16,
                        flipHorizontal: false,
                        minConfidence: 0.1,
                        maxPoseDetections: 1,
                        scoreThreshold: 0.1,
                        detectionType: 'single'
                    });

                    this.log('PoseNet对象创建成功: ' + (this.poseNet !== null));

                    // 添加调试回调
                    this.poseNet.on('pose', (results) => {
                        console.log('🔍 PoseNet回调触发，结果:', results);
                        this.poses = results;

                        if (results && results.length > 0) {
                            // 页面日志（只记录一次）
                            if (!this.firstPoseLogged) {
                                this.log(`✅ 检测到 ${results.length} 个姿态`);
                                this.log('姿态数据结构:');
                                this.log(JSON.stringify(results[0], null, 2));
                                if (results[0].pose && results[0].pose.keypoints) {
                                    this.log(`关键点数量: ${results[0].pose.keypoints.length}`);
                                    this.log('第一个关键点: ' + JSON.stringify(results[0].pose.keypoints[0], null, 2));
                                }
                                this.firstPoseLogged = true;
                            }

                            // 控制台详细输出（可控制）
                            if (this.showConsoleOutput) {
                                console.group('🎯 PoseNet实时检测结果');
                                console.log('时间戳:', new Date().toLocaleTimeString());
                                console.log('检测到的姿态数量:', results.length);

                                results.forEach((result, index) => {
                                    console.group(`姿态 ${index + 1}`);
                                    console.log('整体置信度:', Math.round(result.pose.score * 100) / 100);
                                    console.log('关键点数量:', result.pose.keypoints.length);

                                    // 输出所有关键点的表格
                                    console.table(result.pose.keypoints.map(kp => ({
                                        部位: kp.part,
                                        X坐标: Math.round(kp.position.x),
                                        Y坐标: Math.round(kp.position.y),
                                        置信度: Math.round(kp.score * 100) / 100,
                                        可见: kp.score > 0.1 ? '✅' : '❌'
                                    })));

                                    // 统计高置信度关键点
                                    const highConfidencePoints = result.pose.keypoints.filter(kp => kp.score > 0.1);
                                    console.log(`可见关键点: ${highConfidencePoints.length}/${result.pose.keypoints.length}`);

                                    // 输出关键部位的坐标
                                    const keyParts = ['nose', 'leftEye', 'rightEye', 'leftWrist', 'rightWrist'];
                                    const keyPartData = {};
                                    keyParts.forEach(part => {
                                        const point = result.pose.keypoints.find(kp => kp.part === part);
                                        if (point && point.score > 0.1) {
                                            keyPartData[part] = `(${Math.round(point.position.x)}, ${Math.round(point.position.y)})`;
                                        }
                                    });
                                    if (Object.keys(keyPartData).length > 0) {
                                        console.log('关键部位坐标:', keyPartData);
                                    }

                                    console.groupEnd();
                                });

                                console.groupEnd();
                            }

                        } else {
                            console.log('⚠️ PoseNet回调触发但无姿态数据，results:', results);
                            if (!this.noPoseLogged) {
                                this.log('⚠️ 暂未检测到姿态');
                                this.noPoseLogged = true;
                            }
                        }
                    });

                    // 添加错误处理
                    this.poseNet.on('error', (error) => {
                        console.error('PoseNet错误:', error);
                        this.log('PoseNet错误: ' + error.message);
                    });

                    // 测试手动检测
                    setTimeout(() => {
                        this.log('尝试手动检测...');
                        this.testManualDetection();
                    }, 2000);

                    this.isDetecting = true;
                    this.startDrawLoop();
                    
                    this.updateStatus('姿态检测已启动', 'success');
                    document.getElementById('startDetectionBtn').disabled = true;
                    document.getElementById('stopDetectionBtn').disabled = false;
                    
                    this.log('PoseNet初始化完成，开始检测');

                } catch (error) {
                    this.log('PoseNet初始化失败: ' + error.message);
                    this.updateStatus('检测启动失败: ' + error.message, 'error');
                }
            }

            startDrawLoop() {
                const draw = (currentTime) => {
                    if (!this.isDetecting) return;

                    // FPS限制 - 减少绘制频率
                    if (currentTime - this.lastFrameTime < 1000 / this.targetFPS) {
                        requestAnimationFrame(draw);
                        return;
                    }
                    this.lastFrameTime = currentTime;

                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                    // 绘制视频帧
                    this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

                    // 绘制姿态（简化版本）
                    if (this.poses.length > 0) {
                        this.drawPoseOptimized(this.poses[0]);
                    }

                    requestAnimationFrame(draw);
                };
                draw(performance.now());
            }

            drawPoseOptimized(pose) {
                if (!pose.pose || !pose.pose.keypoints) return;

                const keypoints = pose.pose.keypoints;

                // 只绘制关键点，不绘制标签（性能优化）
                this.ctx.fillStyle = '#ff0000';
                keypoints.forEach((keypoint) => {
                    if (keypoint.score > 0.5) {
                        this.ctx.beginPath();
                        this.ctx.arc(
                            keypoint.position.x,
                            keypoint.position.y,
                            6, 0, 2 * Math.PI  // 稍小的关键点
                        );
                        this.ctx.fill();

                        // 可选：绘制标签
                        if (this.showLabels) {
                            this.ctx.fillStyle = '#ffffff';
                            this.ctx.font = '10px Arial';
                            this.ctx.fillText(
                                keypoint.part,
                                keypoint.position.x + 8,
                                keypoint.position.y - 8
                            );
                            this.ctx.fillStyle = '#ff0000';
                        }
                    }
                });

                // 可选：绘制骨架
                if (this.showSkeleton) {
                    this.drawSkeletonOptimized(keypoints);
                }
            }

            // 保留原始绘制函数用于调试
            drawPose(pose) {
                if (!pose.pose || !pose.pose.keypoints) {
                    this.log('⚠️ 姿态数据结构错误');
                    return;
                }

                const keypoints = pose.pose.keypoints;

                if (!this.drawPoseLogged) {
                    this.log(`🎨 开始绘制姿态，关键点数量: ${keypoints.length}`);
                    this.log('关键点示例: ' + JSON.stringify(keypoints[0], null, 2));
                    this.drawPoseLogged = true;
                }

                // 绘制关键点
                this.ctx.fillStyle = '#ff0000';
                let visiblePoints = 0;
                keypoints.forEach((keypoint, index) => {
                    if (keypoint.score > 0.5) {
                        visiblePoints++;
                        this.ctx.beginPath();
                        this.ctx.arc(
                            keypoint.position.x,
                            keypoint.position.y,
                            8, 0, 2 * Math.PI
                        );
                        this.ctx.fill();

                        // 绘制关键点标签
                        if (this.showLabels) {
                            this.ctx.fillStyle = '#ffffff';
                            this.ctx.font = '12px Arial';
                            this.ctx.fillText(
                                keypoint.part,
                                keypoint.position.x + 10,
                                keypoint.position.y - 10
                            );
                            this.ctx.fillStyle = '#ff0000';
                        }
                    }
                });

                if (!this.visiblePointsLogged) {
                    this.log(`👁️ 可见关键点数量: ${visiblePoints}/${keypoints.length}`);
                    this.visiblePointsLogged = true;
                }

                // 绘制骨架
                if (this.showSkeleton) {
                    this.drawSkeleton(keypoints);
                }
            }

            drawSkeletonOptimized(keypoints) {
                const connections = [
                    [0, 1], [0, 2], [1, 3], [2, 4], // 头部
                    [5, 6], [5, 7], [7, 9], [6, 8], [8, 10], // 手臂
                    [5, 11], [6, 12], [11, 12], // 躯干
                    [11, 13], [13, 15], [12, 14], [14, 16] // 腿部
                ];

                this.ctx.strokeStyle = '#00ff00';
                this.ctx.lineWidth = 2;  // 稍细的线条

                connections.forEach(([startIdx, endIdx]) => {
                    const startPoint = keypoints[startIdx];
                    const endPoint = keypoints[endIdx];

                    if (startPoint && endPoint && startPoint.score > 0.5 && endPoint.score > 0.5) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(startPoint.position.x, startPoint.position.y);
                        this.ctx.lineTo(endPoint.position.x, endPoint.position.y);
                        this.ctx.stroke();
                    }
                });
            }

            drawSkeleton(keypoints) {
                const connections = [
                    [0, 1], [0, 2], [1, 3], [2, 4], // 头部
                    [5, 6], [5, 7], [7, 9], [6, 8], [8, 10], // 手臂
                    [5, 11], [6, 12], [11, 12], // 躯干
                    [11, 13], [13, 15], [12, 14], [14, 16] // 腿部
                ];

                this.ctx.strokeStyle = '#00ff00';
                this.ctx.lineWidth = 3;

                let drawnConnections = 0;
                connections.forEach(([startIdx, endIdx]) => {
                    const startPoint = keypoints[startIdx];
                    const endPoint = keypoints[endIdx];

                    if (startPoint && endPoint && startPoint.score > 0.5 && endPoint.score > 0.5) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(startPoint.position.x, startPoint.position.y);
                        this.ctx.lineTo(endPoint.position.x, endPoint.position.y);
                        this.ctx.stroke();
                        drawnConnections++;
                    }
                });

                if (!this.skeletonLogged) {
                    this.log(`🦴 绘制骨架连接: ${drawnConnections}/${connections.length}`);
                    this.skeletonLogged = true;
                }
            }

            stopDetection() {
                this.isDetecting = false;
                this.updateStatus('检测已停止', 'info');
                document.getElementById('startDetectionBtn').disabled = false;
                document.getElementById('stopDetectionBtn').disabled = true;
                this.startPreview();
                this.log('检测已停止');
            }

            toggleSkeleton() {
                this.showSkeleton = !this.showSkeleton;
                this.log('骨骼显示: ' + (this.showSkeleton ? '开启' : '关闭'));
            }

            clearConsole() {
                console.clear();
                this.log('🧹 控制台已清空');
            }

            toggleConsoleOutput() {
                this.showConsoleOutput = !this.showConsoleOutput;
                const btn = document.getElementById('toggleConsoleOutputBtn');
                btn.textContent = this.showConsoleOutput ? '关闭控制台输出' : '开启控制台输出';
                this.log(`控制台输出: ${this.showConsoleOutput ? '开启' : '关闭'}`);
            }

            toggleLabels() {
                this.showLabels = !this.showLabels;
                const btn = document.getElementById('toggleLabelsBtn');
                btn.textContent = this.showLabels ? '隐藏关键点标签' : '显示关键点标签';
                this.log(`关键点标签: ${this.showLabels ? '显示' : '隐藏'}`);
            }

            async testManualDetection() {
                try {
                    this.log('执行手动检测测试...');

                    if (!this.poseNet) {
                        this.log('❌ PoseNet对象不存在');
                        return;
                    }

                    // 尝试手动调用检测
                    const results = await this.poseNet.singlePose(this.video);
                    console.log('手动检测结果:', results);
                    this.log('手动检测完成，结果: ' + JSON.stringify(results, null, 2));

                } catch (error) {
                    console.error('手动检测失败:', error);
                    this.log('手动检测失败: ' + error.message);

                    // 如果ml5 PoseNet失败，尝试MediaPipe
                    this.log('尝试切换到MediaPipe Pose...');
                    this.initMediaPipePose();
                }
            }

            async initMediaPipePose() {
                try {
                    this.log('初始化MediaPipe Pose...');

                    // 动态加载MediaPipe
                    if (typeof window.MediaPipe === 'undefined') {
                        await this.loadMediaPipeScript();
                    }

                    // 这里会实现MediaPipe的初始化
                    this.log('MediaPipe脚本加载完成，开始初始化...');

                } catch (error) {
                    console.error('MediaPipe初始化失败:', error);
                    this.log('MediaPipe初始化失败: ' + error.message);
                }
            }

            async loadMediaPipeScript() {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/pose@0.5.1675469404/pose.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }

            async tryTensorFlowPose() {
                try {
                    this.log('尝试使用 TensorFlow.js PoseNet...');

                    if (!this.video || this.video.readyState < 2) {
                        this.log('❌ 视频未准备好');
                        return;
                    }

                    // 停止当前检测
                    this.stopDetection();

                    // 初始化TensorFlow.js PoseNet
                    const detector = await poseDetection.createDetector(
                        poseDetection.SupportedModels.PoseNet,
                        {
                            architecture: 'MobileNetV1',
                            outputStride: 16,
                            inputResolution: { width: 640, height: 480 },
                            multiplier: 0.75,
                            quantBytes: 2
                        }
                    );

                    this.log('✅ TensorFlow.js PoseNet 初始化成功');
                    this.tensorFlowDetector = detector;
                    this.isDetecting = true;
                    this.startTensorFlowDetection();

                    document.getElementById('tryTensorFlowBtn').disabled = true;
                    document.getElementById('stopDetectionBtn').disabled = false;
                    this.updateStatus('TensorFlow.js 姿态检测已启动', 'success');

                } catch (error) {
                    this.log('❌ TensorFlow.js 初始化失败: ' + error.message);
                    console.error('TensorFlow.js 错误:', error);
                }
            }

            async startTensorFlowDetection() {
                const detect = async () => {
                    if (!this.isDetecting || !this.tensorFlowDetector) return;

                    try {
                        const poses = await this.tensorFlowDetector.estimatePoses(this.video);

                        if (poses && poses.length > 0) {
                            console.group('🎯 TensorFlow.js PoseNet 检测结果');
                            console.log('时间戳:', new Date().toLocaleTimeString());
                            console.log('检测到的姿态数量:', poses.length);

                            poses.forEach((pose, index) => {
                                console.group(`姿态 ${index + 1}`);
                                console.log('整体置信度:', Math.round(pose.score * 100) / 100);
                                console.log('关键点数量:', pose.keypoints.length);

                                console.table(pose.keypoints.map(kp => ({
                                    部位: kp.name,
                                    X坐标: Math.round(kp.x),
                                    Y坐标: Math.round(kp.y),
                                    置信度: Math.round(kp.score * 100) / 100,
                                    可见: kp.score > 0.1 ? '✅' : '❌'
                                })));

                                console.groupEnd();
                            });

                            console.groupEnd();

                            // 更新poses数据用于绘制
                            this.poses = poses.map(pose => ({
                                pose: {
                                    keypoints: pose.keypoints.map(kp => ({
                                        part: kp.name,
                                        position: { x: kp.x, y: kp.y },
                                        score: kp.score
                                    })),
                                    score: pose.score
                                }
                            }));
                        }

                        requestAnimationFrame(detect);

                    } catch (error) {
                        this.log('❌ TensorFlow.js 检测错误: ' + error.message);
                        console.error('TensorFlow.js 检测错误:', error);
                    }
                };

                detect();
            }

            updateStatus(message, type) {
                const statusEl = document.getElementById('status');
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
            }

            log(message) {
                const debugEl = document.getElementById('debugInfo');
                const timestamp = new Date().toLocaleTimeString();
                debugEl.textContent += `[${timestamp}] ${message}\n`;
                debugEl.scrollTop = debugEl.scrollHeight;
                console.log(message);
            }
        }

        // 启动调试器
        window.addEventListener('load', () => {
            new PoseDebugger();
        });
    </script>
</body>
</html>
