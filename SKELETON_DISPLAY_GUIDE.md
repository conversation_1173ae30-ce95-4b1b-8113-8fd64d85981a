# 🦴 骨骼显示功能使用指南

## 🎯 功能说明

我已经为你的应用添加了骨骼和关节点显示的开关控制功能。现在你可以选择是否在摄像头画面中显示AI识别的骨骼连线和关节点。

## ✅ 已实现的功能

### 1. 显示控制开关
- **位置**: 左侧控制面板中的切换开关
- **功能**: 开启/关闭骨骼和关节点显示
- **默认状态**: 开启（显示骨骼）

### 2. 实时切换
- **即时生效**: 切换开关后立即生效
- **状态保持**: 在检测过程中可以随时切换
- **反馈提示**: 切换时会显示状态信息

### 3. 兼容性支持
- **新旧API**: 兼容ml5.js新旧版本的数据结构
- **多种模式**: 支持姿态检测和手势检测的显示控制
- **调试信息**: 提供详细的调试信息

## 🧪 测试页面

### 1. 主应用 (`http://127.0.0.1:8080`)
- 完整的功能测试
- 左侧面板有"显示骨骼和关节点"开关
- 支持姿态和手势检测的显示控制

### 2. 骨骼测试页面 (`http://127.0.0.1:8080/skeleton-test.html`)
- 专门的骨骼显示测试
- 更详细的显示控制选项：
  - 显示骨骼连接线
  - 显示关键点
  - 显示关键点标签
- 实时调试信息显示

### 3. 简化测试页面 (`http://127.0.0.1:8080/simple-pose.html`)
- 基础的骨骼显示测试
- 简单的开关控制

## 🎮 使用方法

### 基本使用流程
1. **打开应用** → `http://127.0.0.1:8080`
2. **启用摄像头** → 点击"启用摄像头"按钮
3. **开始检测** → 点击"开始检测"按钮
4. **控制显示** → 使用"显示骨骼和关节点"开关

### 显示效果说明
- **开启状态**: 
  - 🔴 红色圆点：关键点位置
  - 🟢 绿色线条：骨骼连接线
  - 🏷️ 白色标签：关键点名称

- **关闭状态**:
  - 只显示纯净的摄像头画面
  - 便于查看原始视频效果

## 🔧 技术实现

### 数据结构兼容
```javascript
// 新API (ml5.js 1.0+)
pose.keypoints → 直接包含关键点数组

// 旧API (ml5.js 0.x)
pose.pose.keypoints → 嵌套的关键点数组
```

### 显示控制逻辑
```javascript
// 在绘制循环中检查显示设置
if (this.showSkeleton) {
    this.drawPose(poses[0]); // 绘制骨骼和关节点
}
// 否则只显示视频画面
```

### 骨骼连接定义
```javascript
const connections = [
    [0, 1], [0, 2], [1, 3], [2, 4], // 头部
    [5, 6], [5, 7], [7, 9], [6, 8], [8, 10], // 手臂
    [5, 11], [6, 12], [11, 12], // 躯干
    [11, 13], [13, 15], [12, 14], [14, 16] // 腿部
];
```

## 🐛 故障排除

### 常见问题

1. **开关没有反应**
   - 确保已经启动了检测
   - 检查浏览器控制台是否有错误
   - 尝试刷新页面重新开始

2. **骨骼显示不正确**
   - 确保光线充足
   - 保持身体完全在画面内
   - 检查AI模型是否正确加载

3. **性能问题**
   - 关闭骨骼显示可以提高性能
   - 减少其他占用资源的应用

### 调试方法

1. **使用专门测试页面**
   ```
   http://127.0.0.1:8080/skeleton-test.html
   ```
   - 查看详细的调试信息
   - 测试各种显示选项

2. **检查浏览器控制台**
   ```javascript
   // 查看检测数据
   console.log('姿态数据:', poses);
   
   // 查看显示状态
   console.log('显示骨骼:', showSkeleton);
   ```

3. **验证数据结构**
   - 检查关键点数量是否正确
   - 确认置信度阈值设置
   - 验证坐标转换是否正确

## 🎨 自定义选项

### 修改显示样式
```javascript
// 在drawPose方法中修改
ctx.strokeStyle = '#00ff00'; // 骨骼线条颜色
ctx.lineWidth = 3;           // 线条粗细
ctx.fillStyle = '#ff0000';   // 关键点颜色
```

### 调整置信度阈值
```javascript
// 只显示高置信度的关键点
if (confidence > 0.7) { // 提高阈值
    // 绘制关键点
}
```

### 添加更多显示选项
- 可以添加颜色选择
- 可以添加大小调节
- 可以添加透明度控制

## 📊 性能优化

### 显示控制的性能影响
- **开启骨骼显示**: 额外的绘制计算
- **关闭骨骼显示**: 只绘制视频，性能最佳
- **建议**: 在性能较低的设备上可以关闭显示

### 优化建议
1. **按需绘制**: 只在需要时绘制骨骼
2. **减少标签**: 关闭关键点标签可以提高性能
3. **降低精度**: 调整置信度阈值减少绘制数量

---

## 🎉 测试建议

现在请按照以下步骤测试骨骼显示功能：

1. **打开主应用**: `http://127.0.0.1:8080`
2. **启用摄像头**: 允许权限并查看画面
3. **开始检测**: 启动AI姿态识别
4. **切换显示**: 使用开关控制骨骼显示
5. **测试专门页面**: 访问骨骼测试页面查看详细功能

如果遇到问题，请查看浏览器控制台的错误信息，或使用专门的测试页面进行调试。
