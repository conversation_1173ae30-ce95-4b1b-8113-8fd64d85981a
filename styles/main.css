/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    margin-bottom: 30px;
}

/* 控制面板 */
.control-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.status-info {
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.label {
    font-weight: 600;
    color: #555;
}

.status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status.connected {
    background: #d4edda;
    color: #155724;
}

.status.loading {
    background: #fff3cd;
    color: #856404;
}

.status.ready {
    background: #d1ecf1;
    color: #0c5460;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
}

/* 按钮样式 */
.action-controls {
    margin-bottom: 20px;
}

.btn {
    width: 100%;
    padding: 12px;
    margin-bottom: 10px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-primary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* 检测模式选择器 */
.detection-mode {
    margin-bottom: 20px;
}

.detection-mode label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

/* 显示控制 */
.display-controls {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* 切换开关样式 */
.toggle-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.toggle-container input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 24px;
    transition: background 0.3s ease;
    margin-right: 12px;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-container input[type="checkbox"]:checked + .toggle-slider {
    background: #667eea;
}

.toggle-container input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.95rem;
}

/* 动作选择器 */
.pose-selector {
    margin-top: 20px;
}

.pose-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.select {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.select:focus {
    outline: none;
    border-color: #667eea;
}

/* 视频容器 */
.video-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

#video-wrapper {
    position: relative;
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 姿态信息 */
.pose-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.pose-guide, .pose-feedback {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
}

.pose-guide h3, .pose-feedback h3 {
    margin-bottom: 10px;
    color: #495057;
    font-size: 1.1rem;
}

#pose-instructions, #pose-feedback {
    color: #6c757d;
    line-height: 1.5;
}

/* 评分面板 */
.scoring-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.scoring-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.score-display {
    text-align: center;
    margin-bottom: 20px;
}

.current-score, .best-score {
    margin-bottom: 15px;
}

.score-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.score-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    transition: all 0.3s ease;
    position: relative;
}

.score-value.score-increase {
    animation: scoreIncrease 0.6s ease-out;
}

.score-value.perfect-score {
    color: #28a745;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.score-value.good-score {
    color: #ffc107;
    text-shadow: 0 0 8px rgba(255, 193, 7, 0.4);
}

@keyframes scoreIncrease {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        color: #28a745;
        text-shadow: 0 0 15px rgba(40, 167, 69, 0.8);
    }
    100% {
        transform: scale(1);
    }
}

.score-breakdown h4 {
    margin-bottom: 15px;
    color: #495057;
    text-align: center;
}

.metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.metric-name {
    color: #6c757d;
    font-size: 0.9rem;
}

.metric-value {
    font-weight: 600;
    color: #495057;
}

/* 游戏化元素样式 */
.game-stats {
    margin: 15px 0;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.level-display {
    margin-bottom: 10px;
}

.level-label {
    font-weight: 600;
    color: #495057;
    font-size: 1.1rem;
}

.level-progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin-top: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.combo-display {
    margin: 10px 0;
    text-align: center;
}

.combo-text {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 5px;
    min-height: 1.5rem;
}

.combo-count {
    font-size: 0.9rem;
    color: #6c757d;
}

.stats-row {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.achievements-panel {
    margin-top: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-radius: 10px;
    border: 1px solid #ffeaa7;
}

.achievements-panel h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 1rem;
}

.achievements-list {
    min-height: 40px;
}

.no-achievements {
    color: #6c757d;
    font-style: italic;
    font-size: 0.9rem;
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border-left: 4px solid #ffc107;
    animation: achievementPop 0.5s ease-out;
}

.achievement-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

.achievement-content {
    flex: 1;
}

.achievement-name {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.achievement-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

@keyframes achievementPop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 底部 */
.footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .control-panel, .scoring-panel {
        order: 2;
    }
    
    .video-container {
        order: 1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .pose-info {
        grid-template-columns: 1fr;
    }
}
