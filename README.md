# Rhythm Pose - AI 动作识别与评分系统

基于 ml5.js 的实时动作识别和评分系统，支持多种瑜伽姿势和运动动作的检测与评估。

## 🎯 功能特点

- **多模式检测**: 支持人体姿势、手部动作、以及组合检测
- **实时动作识别**: 使用 ml5.js PoseNet 和 HandPose 模型进行实时检测
- **丰富动作库**:
  - 人体姿势：树式、战士式、平板支撑、深蹲、开合跳
  - 手部动作：挥手、点赞、比心、握拳、张开手掌
- **智能评分系统**: 基于准确度、稳定性和持续时间的综合评分
- **实时反馈**: 提供即时的动作指导和改进建议
- **可视化显示**: 实时显示骨架关键点和手部关键点
- **响应式设计**: 适配桌面和移动设备

## 🚀 快速开始

### 环境要求

- 现代浏览器（支持 WebRTC 和 WebGL）
- 摄像头设备
- HTTPS 环境（本地开发可使用 localhost）

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd rhythm-pose
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**
   打开浏览器访问 `http://localhost:8080`

### 生产环境部署

```bash
npm run start
```

## 📱 使用说明

### 基本操作

1. **启用摄像头**: 点击"启用摄像头"按钮，允许浏览器访问摄像头
2. **查看画面**: 中间屏幕会显示摄像头实时画面
3. **选择检测模式**:
   - **人体姿势**: 检测全身动作和姿态
   - **手部动作**: 专注于手部手势识别
   - **姿势+手部**: 同时检测身体和手部动作
4. **选择动作**: 根据检测模式选择要练习的动作
5. **开始检测**: 点击"开始检测"按钮启动AI识别
6. **跟随指导**: 根据动作指导完成相应姿势
7. **查看反馈**: 实时查看检测结果和改进建议
8. **关闭摄像头**: 可随时点击"关闭摄像头"停止使用

### 支持的动作

#### 人体姿势检测
- **树式 (瑜伽)**: 单腿站立平衡姿势
- **战士式 (瑜伽)**: 前腿弯曲后腿伸直的力量姿势
- **平板支撑**: 核心力量训练动作
- **深蹲**: 下肢力量训练动作
- **开合跳**: 有氧运动动作

#### 手部动作检测
- **挥手**: 手掌张开左右摆动
- **点赞**: 竖起拇指的手势
- **比心/胜利**: 伸出食指和中指的V字手势
- **握拳**: 五指紧握的拳头
- **张开手掌**: 五指完全展开的手掌

### 评分系统

评分基于三个维度：

- **准确度 (50%)**: 姿势与标准动作的匹配程度
- **稳定性 (30%)**: 动作保持的稳定程度
- **持续时间 (20%)**: 正确姿势的保持时间

## 🛠️ 技术架构

### 核心技术

- **ml5.js**: 机器学习库，提供 PoseNet 姿态估计
- **p5.js**: 创意编程库，用于视频处理和可视化
- **Vanilla JavaScript**: 原生 JavaScript 实现
- **CSS Grid & Flexbox**: 响应式布局

### 项目结构

```
rhythm-pose/
├── index.html              # 主页面
├── styles/
│   └── main.css            # 样式文件
├── js/
│   ├── main.js             # 主应用逻辑
│   ├── pose-detector.js    # 姿态检测器
│   ├── pose-definitions.js # 动作定义
│   └── scoring-system.js   # 评分系统
├── package.json            # 项目配置
└── README.md              # 项目说明
```

### 核心类说明

- **RhythmPoseApp**: 主应用类，协调各个模块
- **PoseDetector**: 姿态检测器，处理摄像头和 AI 模型
- **PoseDefinitions**: 动作定义，包含各种动作的标准参数
- **ScoringSystem**: 评分系统，计算动作质量分数

## 🔧 开发指南

### 添加新动作

1. 在 `pose-definitions.js` 中添加新的动作定义
2. 在 `scoring-system.js` 中实现对应的评估逻辑
3. 更新 UI 选择器

### 自定义评分算法

修改 `ScoringSystem` 类中的评估方法：

```javascript
// 示例：添加新的评估逻辑
evaluateCustomPose(keypoints) {
    let score = 0;
    // 实现自定义评分逻辑
    return score;
}
```

### 调整检测参数

在 `PoseDetector` 类中修改检测配置：

```javascript
this.detectionConfig = {
    imageScaleFactor: 0.3,  // 图像缩放因子
    outputStride: 16,       // 输出步长
    minConfidence: 0.5,     // 最小置信度
    // ... 其他参数
};
```

## 🎨 界面定制

### 修改主题色彩

在 `main.css` 中修改 CSS 变量：

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #00ff00;
}
```

### 响应式断点

```css
/* 平板设备 */
@media (max-width: 1200px) { ... }

/* 手机设备 */
@media (max-width: 768px) { ... }
```

## 🚧 已知限制

- 需要良好的光照条件
- 摄像头质量影响检测精度
- 某些复杂动作可能识别不够准确
- 需要 HTTPS 环境才能访问摄像头

## 🔮 未来计划

- [ ] 集成 ZKTLS 隐私保护
- [ ] 添加区块链记录功能
- [ ] 支持更多动作类型
- [ ] 添加语音指导
- [ ] 实现多人对战模式
- [ ] 优化移动端体验

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题或建议，请联系项目维护者。
