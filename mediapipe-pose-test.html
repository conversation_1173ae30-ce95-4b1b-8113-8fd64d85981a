<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediaPipe Pose 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            position: relative;
            margin: 20px 0;
        }
        video, canvas {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
        }
        .controls {
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 MediaPipe Pose 性能测试</h1>
        <p>测试MediaPipe Pose的性能表现</p>

        <div class="controls">
            <button class="btn btn-primary" onclick="enableCamera()">启用摄像头</button>
            <button class="btn btn-success" onclick="startMediaPipePose()">开始MediaPipe Pose</button>
            <button class="btn btn-danger" onclick="stopDetection()">停止检测</button>
        </div>

        <div class="video-container">
            <video id="video" autoplay muted playsinline></video>
            <canvas id="canvas"></canvas>
        </div>

        <div id="log" class="log">等待开始测试...</div>
    </div>

    <!-- 加载必要的库 -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.21.0/dist/tf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/pose-detection@2.0.0/dist/pose-detection.min.js"></script>

    <script>
        let video, canvas, ctx;
        let detector = null;
        let isDetecting = false;
        let detectionId = null;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        async function enableCamera() {
            try {
                video = document.getElementById('video');
                canvas = document.getElementById('canvas');
                ctx = canvas.getContext('2d');

                log('正在启用摄像头...');

                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 }
                });

                video.srcObject = stream;
                
                video.addEventListener('loadedmetadata', () => {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    log(`摄像头启用成功: ${video.videoWidth}x${video.videoHeight}`);
                });

            } catch (error) {
                log('摄像头启用失败: ' + error.message);
            }
        }

        async function startMediaPipePose() {
            try {
                if (!video || video.readyState < 2) {
                    log('请先启用摄像头');
                    return;
                }

                log('初始化 MediaPipe Pose...');

                // 创建MediaPipe Pose检测器
                detector = await poseDetection.createDetector(
                    poseDetection.SupportedModels.BlazePose,
                    {
                        runtime: 'tfjs',
                        modelType: 'lite',
                        enableSmoothing: true,
                        enableSegmentation: false
                    }
                );

                log('MediaPipe Pose 初始化成功');
                isDetecting = true;

                // 开始检测循环
                let frameCount = 0;
                let startTime = performance.now();

                async function detect() {
                    if (!isDetecting) return;

                    try {
                        const detectStart = performance.now();
                        const poses = await detector.estimatePoses(video);
                        const detectTime = performance.now() - detectStart;

                        frameCount++;

                        if (poses && poses.length > 0) {
                            // 清除画布
                            ctx.clearRect(0, 0, canvas.width, canvas.height);
                            
                            // 绘制关键点
                            const pose = poses[0];
                            pose.keypoints.forEach(keypoint => {
                                if (keypoint.score > 0.3) {
                                    ctx.beginPath();
                                    ctx.arc(keypoint.x, keypoint.y, 5, 0, 2 * Math.PI);
                                    ctx.fillStyle = '#00ff00';
                                    ctx.fill();
                                }
                            });

                            // 每30帧输出一次性能信息
                            if (frameCount % 30 === 0) {
                                const elapsed = performance.now() - startTime;
                                const fps = (frameCount * 1000) / elapsed;
                                log(`检测性能: ${fps.toFixed(1)} FPS, 检测时间: ${detectTime.toFixed(2)}ms, 关键点: ${pose.keypoints.length}`);
                            }
                        }

                        detectionId = requestAnimationFrame(detect);

                    } catch (error) {
                        log('检测错误: ' + error.message);
                        detectionId = requestAnimationFrame(detect);
                    }
                }

                detect();

            } catch (error) {
                log('MediaPipe Pose 初始化失败: ' + error.message);
            }
        }

        function stopDetection() {
            isDetecting = false;
            if (detectionId) {
                cancelAnimationFrame(detectionId);
                detectionId = null;
            }
            log('检测已停止');
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('MediaPipe Pose 测试页面已加载');
        });
    </script>
</body>
</html>
