# 🤖 ml5.js 功能总览

## 📋 ml5.js 完整功能列表

ml5.js 是一个友好的机器学习JavaScript库，基于TensorFlow.js构建。以下是其主要功能模块：

### 🎯 计算机视觉 (Computer Vision)

#### 1. **BodyPose** (人体姿态估计)
- **功能**: 检测人体17个关键点
- **模型**: MoveNet, BlazePose
- **用途**: 姿态识别、动作分析、健身应用
- **输出**: 关键点坐标、置信度、骨骼连接

#### 2. **HandPose** (手部姿态估计)
- **功能**: 检测手部21个关键点
- **模型**: MediaPipe Hands
- **用途**: 手势识别、手语翻译、交互控制
- **输出**: 手指关节坐标、手部方向

#### 3. **FaceMesh** (面部网格)
- **功能**: 检测面部468个关键点
- **模型**: MediaPipe FaceMesh
- **用途**: 面部表情识别、AR滤镜、情感分析
- **输出**: 面部特征点、面部轮廓

#### 4. **ImageClassifier** (图像分类)
- **功能**: 识别图像中的物体
- **模型**: MobileNet, DarkNet等
- **用途**: 物体识别、场景分类
- **输出**: 分类标签、置信度

#### 5. **ObjectDetector** (物体检测)
- **功能**: 检测并定位图像中的多个物体
- **模型**: COCO-SSD, YOLO
- **用途**: 实时物体检测、安防监控
- **输出**: 边界框、物体类别、位置

#### 6. **FeatureExtractor** (特征提取)
- **功能**: 从图像中提取特征向量
- **用途**: 图像相似度比较、自定义分类器
- **输出**: 特征向量

### 🎵 音频处理 (Audio)

#### 7. **SoundClassifier** (声音分类)
- **功能**: 识别环境声音
- **模型**: Speech Commands
- **用途**: 语音命令识别、环境音识别
- **输出**: 声音类别、置信度

#### 8. **PitchDetection** (音调检测)
- **功能**: 检测音频的基频
- **用途**: 音乐应用、调音器
- **输出**: 频率值

### 📝 自然语言处理 (NLP)

#### 9. **Sentiment** (情感分析)
- **功能**: 分析文本的情感倾向
- **用途**: 社交媒体分析、客户反馈
- **输出**: 情感分数 (正面/负面)

#### 10. **Word2Vec** (词向量)
- **功能**: 将词语转换为向量表示
- **用途**: 语义相似度、文本分析
- **输出**: 词向量、相似词

### 🎨 生成模型 (Generative)

#### 11. **StyleTransfer** (风格迁移)
- **功能**: 将艺术风格应用到图像
- **用途**: 艺术创作、图像处理
- **输出**: 风格化图像

#### 12. **Pix2Pix** (图像到图像转换)
- **功能**: 图像条件生成
- **用途**: 草图转照片、图像修复
- **输出**: 生成的图像

#### 13. **DCGAN** (深度卷积生成对抗网络)
- **功能**: 生成新的图像
- **用途**: 创意设计、数据增强
- **输出**: 生成的图像

### 🔧 工具和实用功能

#### 14. **NeuralNetwork** (神经网络)
- **功能**: 创建和训练自定义神经网络
- **用途**: 自定义机器学习模型
- **输出**: 预测结果

#### 15. **KMeans** (K均值聚类)
- **功能**: 无监督聚类算法
- **用途**: 数据分析、模式识别
- **输出**: 聚类结果

#### 16. **UniversalSentenceEncoder** (通用句子编码器)
- **功能**: 将句子编码为向量
- **用途**: 文本相似度、语义搜索
- **输出**: 句子向量

## 🎯 你的项目中使用的功能

### 当前实现
1. **BodyPose**: 人体姿态检测 ✅
2. **HandPose**: 手部动作识别 ✅

### 可扩展功能
1. **FaceMesh**: 面部表情识别
2. **SoundClassifier**: 语音命令控制
3. **ImageClassifier**: 背景场景识别
4. **Sentiment**: 用户反馈情感分析

## 🔍 骨骼显示问题分析

### ml5.js 自带功能
- **BodyPose**: 提供关键点坐标数据
- **HandPose**: 提供手部关键点数据
- **不包含**: 自动绘制功能（需要手动实现）

### 数据结构
```javascript
// BodyPose 输出
{
  keypoints: [
    { x: 100, y: 200, confidence: 0.9, name: "nose" },
    // ... 17个关键点
  ],
  confidence: 0.85
}

// HandPose 输出
{
  keypoints: [
    { x: 150, y: 250, confidence: 0.8, name: "wrist" },
    // ... 21个关键点
  ],
  handedness: "Left"
}
```

### 绘制实现
ml5.js **不自动绘制**骨骼线，需要：
1. 获取关键点数据
2. 定义骨骼连接关系
3. 手动绘制到Canvas上

## 🐛 当前问题可能原因

1. **API变化**: ml5.js 1.0+ API结构改变
2. **绘制循环**: 新API的绘制机制不同
3. **数据结构**: 关键点数据格式变化
4. **事件处理**: 检测回调机制改变

## 🔧 解决方案

### 1. 使用调试页面
```
http://127.0.0.1:8080/debug-skeleton.html
```
- 查看详细的数据结构
- 测试绘制功能
- 验证开关控制

### 2. 检查数据流
```javascript
// 确认数据接收
bodyPose.detectStart(video, (results) => {
  console.log('检测结果:', results);
  if (results[0]?.keypoints) {
    console.log('关键点数量:', results[0].keypoints.length);
  }
});
```

### 3. 验证绘制逻辑
```javascript
// 确认绘制调用
function drawPose(pose) {
  console.log('开始绘制:', pose);
  // 绘制代码...
}
```

## 📊 性能对比

| 功能 | 计算量 | 实时性 | 精度 | 用途 |
|------|--------|--------|------|------|
| BodyPose | 中等 | 30fps | 高 | 全身动作 |
| HandPose | 低 | 60fps | 很高 | 手部细节 |
| FaceMesh | 高 | 15fps | 极高 | 面部表情 |
| ImageClassifier | 低 | 实时 | 高 | 物体识别 |

## 🚀 扩展建议

基于你的动作识别项目，可以考虑添加：

1. **FaceMesh**: 面部表情评分
2. **SoundClassifier**: 语音指导
3. **ImageClassifier**: 环境适应性
4. **Sentiment**: 用户体验分析

---

**现在请测试调试页面，看看能否正常显示骨骼线！** 🦴
